# ملف التكوين الموحد لقاعدة البيانات
# Unified Database Configuration File
# تم توحيد جميع إعدادات قاعدة البيانات لتستخدم orcl

# ========================================
# إعدادات قاعدة البيانات الأساسية
# ========================================

# خادم قاعدة البيانات
database.host=localhost
database.port=1521
database.service.name=orcl

# بيانات الاعتماد الرئيسية
database.main.username=ship_erp
database.main.password=ship_erp_password

# بيانات الاعتماد للنظام المصدر
database.source.username=ias20251
database.source.password=ys123

# بيانات الاعتماد للإدارة
database.admin.username=ysdba2
database.admin.password=ys123

# ========================================
# URLs الاتصال المختلفة
# ========================================

# الاتصال الرئيسي (SHIP_ERP)
database.main.url=jdbc:oracle:thin:@${database.host}:${database.port}:${database.service.name}

# الاتصال بالنظام المصدر (IAS20251)
database.source.url=jdbc:oracle:thin:@${database.host}:${database.port}:${database.service.name}

# الاتصال للإدارة
database.admin.url=jdbc:oracle:thin:@${database.host}:${database.port}:${database.service.name}

# ========================================
# إعدادات Oracle JDBC
# ========================================

database.driver=oracle.jdbc.OracleDriver
database.dialect=org.hibernate.dialect.OracleDialect

# ========================================
# إعدادات Connection Pool
# ========================================

database.pool.minimumIdle=5
database.pool.maximumPoolSize=20
database.pool.connectionTimeout=30000
database.pool.idleTimeout=600000
database.pool.maxLifetime=1800000

# ========================================
# إعدادات Hibernate
# ========================================

hibernate.hbm2ddl.auto=update
hibernate.show_sql=false
hibernate.format_sql=true
hibernate.use_sql_comments=true
hibernate.jdbc.batch_size=20
hibernate.order_inserts=true
hibernate.order_updates=true
hibernate.jdbc.batch_versioned_data=true

# إعدادات الترميز
hibernate.connection.characterEncoding=UTF-8
hibernate.connection.useUnicode=true

# ========================================
# إعدادات Oracle المتقدمة
# ========================================

oracle.jdbc.autoCommitSpecCompliant=false
oracle.net.CONNECT_TIMEOUT=15000
oracle.jdbc.ReadTimeout=60000
oracle.net.READ_TIMEOUT=60000
oracle.net.disableOob=true
oracle.jdbc.implicitStatementCacheSize=20
oracle.jdbc.defaultRowPrefetch=20

# ========================================
# Database Links
# ========================================

database.link.ias.name=IAS20251_LINK
database.link.ias.connect_to=${database.source.username}
database.link.ias.identified_by=${database.source.password}
database.link.ias.using=${database.host}:${database.port}/${database.service.name}

# ========================================
# إعدادات النسخ الاحتياطي
# ========================================

backup.enabled=true
backup.directory=backup
backup.auto.interval=24
backup.retention.days=30

# ========================================
# إعدادات التسجيل
# ========================================

logging.level.root=INFO
logging.level.oracle=DEBUG
logging.level.hibernate=INFO
logging.file.name=logs/database.log
logging.file.max-size=10MB
logging.file.max-history=30

# ========================================
# إعدادات الأمان
# ========================================

security.connection.encryption=false
security.connection.ssl=false
security.max.connections.per.user=10

# ========================================
# إعدادات الأداء
# ========================================

performance.statement.cache.size=50
performance.result.set.cache.size=100
performance.connection.validation.timeout=5000

# ========================================
# إعدادات التطبيق
# ========================================

app.database.schema.main=SHIP_ERP
app.database.schema.source=IAS20251
app.database.auto.create.tables=true
app.database.auto.update.schema=true

# ========================================
# ملاحظات مهمة
# ========================================

# 1. تم توحيد جميع الاتصالات لتستخدم orcl بدلاً من XE
# 2. يجب التأكد من وجود قاعدة البيانات orcl على الخادم
# 3. يجب إنشاء المستخدمين المطلوبين قبل التشغيل
# 4. تأكد من وجود مكتبات Oracle JDBC في مجلد lib

# ========================================
# أوامر إنشاء المستخدمين
# ========================================

# CREATE USER ship_erp IDENTIFIED BY ship_erp_password;
# GRANT CONNECT, RESOURCE, DBA TO ship_erp;
# GRANT UNLIMITED TABLESPACE TO ship_erp;

# CREATE USER ias20251 IDENTIFIED BY ys123;
# GRANT CONNECT, RESOURCE TO ias20251;
# GRANT UNLIMITED TABLESPACE TO ias20251;
