@echo off
chcp 65001 > nul
title التحقق من التكوين الموحد لقاعدة البيانات

echo ========================================
echo    التحقق من التكوين الموحد لقاعدة البيانات
echo    Verify Unified Database Configuration
echo ========================================
echo.

echo 🔍 فحص ملفات التكوين...
echo.

REM فحص ملف application.properties
echo [1/4] فحص application.properties...
if exist "src\main\resources\application.properties" (
    findstr /C:"orcl" "src\main\resources\application.properties" > nul
    if %errorlevel% equ 0 (
        echo ✅ application.properties يستخدم orcl
    ) else (
        echo ❌ application.properties لا يستخدم orcl
        set ISSUES=1
    )
) else (
    echo ⚠️ application.properties غير موجود
    set ISSUES=1
)

REM فحص ملف hibernate.cfg.xml
echo [2/4] فحص hibernate.cfg.xml...
if exist "src\main\resources\hibernate.cfg.xml" (
    findstr /C:"orcl" "src\main\resources\hibernate.cfg.xml" > nul
    if %errorlevel% equ 0 (
        echo ✅ hibernate.cfg.xml يستخدم orcl
    ) else (
        echo ❌ hibernate.cfg.xml لا يستخدم orcl
        set ISSUES=1
    )
) else (
    echo ⚠️ hibernate.cfg.xml غير موجود
    set ISSUES=1
)

REM فحص ملف TestDatabaseConfig.java
echo [3/4] فحص TestDatabaseConfig.java...
if exist "src\main\java\TestDatabaseConfig.java" (
    findstr /C:"orcl" "src\main\java\TestDatabaseConfig.java" > nul
    if %errorlevel% equ 0 (
        echo ✅ TestDatabaseConfig.java يستخدم orcl
    ) else (
        echo ❌ TestDatabaseConfig.java لا يستخدم orcl
        set ISSUES=1
    )
) else (
    echo ⚠️ TestDatabaseConfig.java غير موجود
)

REM فحص الملف الموحد الجديد
echo [4/4] فحص database-config-unified.properties...
if exist "database-config-unified.properties" (
    echo ✅ ملف التكوين الموحد موجود
) else (
    echo ❌ ملف التكوين الموحد غير موجود
    set ISSUES=1
)

echo.
echo ========================================

if defined ISSUES (
    echo ❌ تم العثور على مشاكل في التكوين!
    echo.
    echo الحلول المقترحة:
    echo 1. تشغيل: fix-database-config.bat
    echo 2. مراجعة الملفات يدوياً
    echo 3. استخدام الملف الموحد: database-config-unified.properties
    echo.
) else (
    echo ✅ جميع ملفات التكوين تستخدم orcl بشكل صحيح!
    echo.
    echo الملفات المتحققة:
    echo - src\main\resources\application.properties
    echo - src\main\resources\hibernate.cfg.xml  
    echo - src\main\java\TestDatabaseConfig.java
    echo - database-config-unified.properties
    echo.
)

echo 📋 معلومات التكوين الحالي:
echo ================================
echo خادم قاعدة البيانات: localhost:1521
echo اسم قاعدة البيانات: orcl
echo المستخدم الرئيسي: ship_erp
echo المستخدم المصدر: ias20251
echo المستخدم الإداري: ysdba2
echo.

echo 🔧 للتحقق من الاتصال:
echo java -cp "lib/*;." OracleConnectionFixer
echo.

echo 📚 للمزيد من المعلومات:
echo راجع ملف: database-config-unified.properties
echo.

pause
