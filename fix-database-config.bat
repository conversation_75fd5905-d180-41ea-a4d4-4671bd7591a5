@echo off
chcp 65001 > nul
title إصلاح تكوين قاعدة البيانات

echo ========================================
echo    إصلاح تكوين قاعدة البيانات
echo    Fix Database Configuration
echo ========================================
echo.

echo 🔧 بدء إصلاح ملفات التكوين...
echo.

REM إنشاء نسخة احتياطية
echo [1/5] إنشاء نسخة احتياطية...
if not exist "backup" mkdir backup
if exist "src\main\resources\application.properties" (
    copy "src\main\resources\application.properties" "backup\application.properties.backup" > nul
    echo ✅ تم إنشاء نسخة احتياطية من application.properties
)
if exist "src\main\resources\hibernate.cfg.xml" (
    copy "src\main\resources\hibernate.cfg.xml" "backup\hibernate.cfg.xml.backup" > nul
    echo ✅ تم إنشاء نسخة احتياطية من hibernate.cfg.xml
)

REM إصلاح application.properties
echo [2/5] إصلاح application.properties...
if exist "src\main\resources\application.properties" (
    powershell -Command "(Get-Content 'src\main\resources\application.properties') -replace ':XE', ':orcl' | Set-Content 'src\main\resources\application.properties'"
    echo ✅ تم إصلاح application.properties
) else (
    echo ⚠️ application.properties غير موجود
)

REM إصلاح hibernate.cfg.xml
echo [3/5] إصلاح hibernate.cfg.xml...
if exist "src\main\resources\hibernate.cfg.xml" (
    powershell -Command "(Get-Content 'src\main\resources\hibernate.cfg.xml') -replace ':XE', ':orcl' | Set-Content 'src\main\resources\hibernate.cfg.xml'"
    echo ✅ تم إصلاح hibernate.cfg.xml
) else (
    echo ⚠️ hibernate.cfg.xml غير موجود
)

REM إصلاح TestDatabaseConfig.java
echo [4/5] إصلاح TestDatabaseConfig.java...
if exist "src\main\java\TestDatabaseConfig.java" (
    powershell -Command "(Get-Content 'src\main\java\TestDatabaseConfig.java') -replace '\"XE\"', '\"orcl\"' | Set-Content 'src\main\java\TestDatabaseConfig.java'"
    echo ✅ تم إصلاح TestDatabaseConfig.java
) else (
    echo ⚠️ TestDatabaseConfig.java غير موجود
)

REM التحقق من النتائج
echo [5/5] التحقق من النتائج...
echo.

echo 🔍 فحص الملفات المُصلحة:
echo ============================

if exist "src\main\resources\application.properties" (
    findstr /C:"orcl" "src\main\resources\application.properties" > nul
    if %errorlevel% equ 0 (
        echo ✅ application.properties: يستخدم orcl
    ) else (
        echo ❌ application.properties: لا يزال يستخدم XE
    )
)

if exist "src\main\resources\hibernate.cfg.xml" (
    findstr /C:"orcl" "src\main\resources\hibernate.cfg.xml" > nul
    if %errorlevel% equ 0 (
        echo ✅ hibernate.cfg.xml: يستخدم orcl
    ) else (
        echo ❌ hibernate.cfg.xml: لا يزال يستخدم XE
    )
)

if exist "src\main\java\TestDatabaseConfig.java" (
    findstr /C:"orcl" "src\main\java\TestDatabaseConfig.java" > nul
    if %errorlevel% equ 0 (
        echo ✅ TestDatabaseConfig.java: يستخدم orcl
    ) else (
        echo ❌ TestDatabaseConfig.java: لا يزال يستخدم XE
    )
)

echo.
echo ========================================
echo 🎉 تم الانتهاء من إصلاح التكوين!
echo.

echo 📋 الملفات المُصلحة:
echo - src\main\resources\application.properties
echo - src\main\resources\hibernate.cfg.xml
echo - src\main\java\TestDatabaseConfig.java
echo.

echo 💾 النسخ الاحتياطية في مجلد: backup\
echo.

echo 🔧 الخطوات التالية:
echo 1. تشغيل: verify-database-config.bat للتحقق
echo 2. تشغيل: java -cp "lib/*;." OracleConnectionFixer لاختبار الاتصال
echo 3. تشغيل التطبيق: run-ship-erp.bat
echo.

echo ⚠️ ملاحظة مهمة:
echo تأكد من وجود قاعدة البيانات orcl على الخادم
echo وأن المستخدمين ship_erp و ias20251 موجودين
echo.

pause
